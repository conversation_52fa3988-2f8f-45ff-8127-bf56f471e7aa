// 云函数API对接服务
import axios from 'axios';

// 配置云开发环境
const CLOUD_ENV_ID = 'cloud1-9gsj7t48183e5a9f'; // 您的实际云环境ID

// 云函数HTTP触发器URL配置
// 注意：这个URL需要在云开发控制台中为adminApi云函数配置HTTP触发器
const CLOUD_BASE_URL = 'https://cloud1-9gsj7t48183e5a9f-1366958750.ap-shanghai.app.tcloudbase.com/adminApi';

// API响应类型定义
interface CloudApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  debug?: any;
}

// 创建axios实例
const cloudApi = axios.create({
  baseURL: CLOUD_BASE_URL,
  timeout: 30000, // 增加超时时间到30秒
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
cloudApi.interceptors.request.use(
  (config) => {
    // 添加管理员认证token
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;

      // 如果是临时管理员token，添加特殊标识
      if (token.startsWith('admin-') || token.startsWith('mock_token_')) {
        config.headers['X-Admin-Temp-Token'] = 'true';
        config.headers['X-Admin-Username'] = 'admin';
      }
    }

    // 添加管理员标识和环境信息
    config.headers['X-Admin-Request'] = 'true';
    config.headers['X-Cloud-Env'] = CLOUD_ENV_ID;
    config.headers['X-Request-Time'] = new Date().toISOString();

    // 添加调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 发送云开发API请求:', {
        url: config.url,
        method: config.method,
        action: config.data?.action,
        data: config.data?.data,
        token: token ? (token.startsWith('admin-') || token.startsWith('mock_token_') ? '临时管理员token' : '真实token') : '无token',
        timestamp: new Date().toLocaleString()
      });
    }

    return config;
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
cloudApi.interceptors.response.use(
  (response) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 云开发API响应:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        headers: response.headers,
        timestamp: new Date().toLocaleString()
      });
    }

    // 检查响应数据格式
    const data = response.data;
    if (typeof data === 'object' && data !== null) {
      // 如果响应包含success字段，记录状态
      if ('success' in data) {
        if (!data.success && data.error) {
          console.warn('⚠️ API返回业务错误:', data.error);
        }
      }
    }

    return response;
  },
  (error) => {
    console.error('❌ 云开发API请求错误:', error);

    // 详细错误信息
    const errorInfo = {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      url: error.config?.url,
      method: error.config?.method,
      timestamp: new Date().toLocaleString()
    };

    console.error('🔍 错误详情:', errorInfo);

    // 处理不同类型的错误
    if (error.response?.status === 401) {
      console.warn('🔐 认证失败，清除token并跳转登录页');
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    } else if (error.response?.status === 403) {
      console.warn('🚫 权限不足');
    } else if (error.response?.status >= 500) {
      console.error('🔥 服务器内部错误');
    } else if (error.code === 'ECONNABORTED') {
      console.error('⏰ 请求超时');
    } else if (error.code === 'ERR_NETWORK') {
      console.error('🌐 网络连接错误');
    }

    return Promise.reject(error);
  }
);

// 通用API调用函数
async function callCloudApi<T = any>(action: string, data?: any): Promise<CloudApiResponse<T>> {
  try {
    const response = await cloudApi.post('/', { action, data });
    return response.data;
  } catch (error: any) {
    // 统一错误处理
    if (error.response?.data) {
      return error.response.data;
    }

    // 网络或其他错误
    return {
      success: false,
      error: error.message || '网络请求失败'
    };
  }
}

// 用户管理API
export const userApi = {
  // 获取用户列表
  getUserList: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    verified?: boolean;
  }): Promise<CloudApiResponse<any[]>> => {
    return callCloudApi('getUserList', params);
  },

  // 获取用户详情
  getUserDetail: async (userId: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getUserDetail', { userId });
  },

  // 更新用户状态
  updateUserStatus: async (userId: string, status: 'active' | 'inactive'): Promise<CloudApiResponse<any>> => {
    return callCloudApi('updateUserStatus', { userId, status });
  },

  // 批量更新用户
  batchUpdateUsers: async (userIds: string[], action: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('batchUpdateUsers', { userIds, action });
  },

  // 获取用户统计
  getUserStats: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getUserStats');
  },

  // 导出用户数据
  exportUsers: async (params?: any): Promise<CloudApiResponse<any>> => {
    return callCloudApi('exportUsers', params);
  },
};

// 订单管理API
export const orderApi = {
  // 获取订单列表
  getOrderList: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    userId?: string;
    dateRange?: [string, string];
    amountRange?: [number, number];
  }): Promise<CloudApiResponse<any[]>> => {
    return callCloudApi('getOrderList', params);
  },

  // 获取订单详情
  getOrderDetail: async (orderId: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getOrderDetail', { orderId });
  },

  // 更新订单状态
  updateOrderStatus: async (orderId: string, status: string, note?: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('updateOrderStatus', { orderId, status, note });
  },

  // 批量更新订单
  batchUpdateOrders: async (orderIds: string[], action: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('batchUpdateOrders', { orderIds, action });
  },

  // 获取订单统计
  getOrderStats: async (dateRange?: [string, string]): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getOrderStats', { dateRange });
  },

  // 导出订单数据
  exportOrders: async (params?: any): Promise<CloudApiResponse<any>> => {
    return callCloudApi('exportOrders', params);
  },

  // 取消订单
  cancelOrder: async (orderId: string, reason: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('cancelOrder', { orderId, reason });
  },

  // 强制完成订单
  forceCompleteOrder: async (orderId: string, note?: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('forceCompleteOrder', { orderId, note });
  },
};

// 聊天管理API
export const chatApi = {
  // 获取聊天室列表
  getChatRoomList: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    orderId?: string;
  }): Promise<CloudApiResponse<any[]>> => {
    return callCloudApi('getChatRoomList', params);
  },

  // 获取聊天消息
  getChatMessages: async (roomId: string, params?: {
    page?: number;
    limit?: number;
    startTime?: string;
    endTime?: string;
  }): Promise<CloudApiResponse<any[]>> => {
    return callCloudApi('getChatMessages', { roomId, ...params });
  },

  // 禁言用户
  banUser: async (userId: string, duration?: number, reason?: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('banUser', { userId, duration, reason });
  },

  // 解除禁言
  unbanUser: async (userId: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('unbanUser', { userId });
  },

  // 删除消息
  deleteMessage: async (messageId: string, reason?: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('deleteMessage', { messageId, reason });
  },

  // 获取聊天统计
  getChatStats: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getChatStats');
  },

  // 关闭聊天室
  closeChatRoom: async (roomId: string, reason?: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('closeChatRoom', { roomId, reason });
  },
};

// 钱包管理API
export const walletApi = {
  // 获取交易记录
  getTransactionList: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
    userId?: string;
    dateRange?: [string, string];
    amountRange?: [number, number];
  }): Promise<CloudApiResponse<any[]>> => {
    return callCloudApi('getTransactionList', params);
  },

  // 审核提现申请
  approveWithdraw: async (transactionId: string, note?: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('approveWithdraw', { transactionId, note });
  },

  // 拒绝提现申请
  rejectWithdraw: async (transactionId: string, reason: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('rejectWithdraw', { transactionId, reason });
  },

  // 批量处理提现申请
  batchProcessWithdraw: async (transactionIds: string[], action: 'approve' | 'reject', reason?: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('batchProcessWithdraw', { transactionIds, action, reason });
  },

  // 获取钱包统计
  getWalletStats: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getWalletStats');
  },

  // 手动调整用户余额
  adjustUserBalance: async (userId: string, amount: number, reason: string, type: 'add' | 'subtract'): Promise<CloudApiResponse<any>> => {
    return callCloudApi('adjustUserBalance', { userId, amount, reason, type });
  },

  // 获取用户钱包详情
  getUserWallet: async (userId: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getUserWallet', { userId });
  },

  // 导出交易记录
  exportTransactions: async (params?: any): Promise<CloudApiResponse<any>> => {
    return callCloudApi('exportTransactions', params);
  },
};

// 通知管理API
export const notificationApi = {
  // 获取通知列表
  getNotificationList: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    search?: string;
  }) => {
    return cloudApi.post('/', { action: 'getNotificationList', data: params });
  },

  // 创建通知
  createNotification: async (notification: {
    title: string;
    content: string;
    type: string;
    isBroadcast?: boolean;
    userId?: string;
  }) => {
    return cloudApi.post('/', { action: 'createNotification', data: notification });
  },

  // 删除通知
  deleteNotification: async (notificationId: string) => {
    return cloudApi.post('/', { action: 'deleteNotification', data: { notificationId } });
  },
};

// 评价管理API
export const evaluationApi = {
  // 获取评价列表
  getEvaluationList: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    score?: number;
  }) => {
    return cloudApi.post('/', { action: 'getEvaluationList', data: params });
  },

  // 审核评价
  approveEvaluation: async (evaluationId: string) => {
    return cloudApi.post('/', { action: 'approveEvaluation', data: { evaluationId } });
  },

  // 拒绝评价
  rejectEvaluation: async (evaluationId: string, reason: string) => {
    return cloudApi.post('/', { action: 'rejectEvaluation', data: { evaluationId, reason } });
  },

  // 获取评价统计
  getEvaluationStats: async () => {
    return cloudApi.post('/', { action: 'getEvaluationStats' });
  },
};

// 仪表盘API
export const dashboardApi = {
  // 获取仪表盘统计数据
  getDashboardStats: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getDashboardStats');
  },

  // 获取图表数据
  getChartData: async (type: 'order' | 'user' | 'revenue', dateRange?: [string, string]): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getChartData', { type, dateRange });
  },

  // 获取最近活动
  getRecentActivities: async (limit?: number): Promise<CloudApiResponse<any[]>> => {
    return callCloudApi('getRecentActivities', { limit });
  },

  // 获取实时统计
  getRealTimeStats: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getRealTimeStats');
  },

  // 获取趋势数据
  getTrendData: async (metric: string, period: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getTrendData', { metric, period });
  },
};

// 认证API
export const authApi = {
  // 管理员登录
  login: async (username: string, password: string): Promise<CloudApiResponse<{ token: string; admin: any }>> => {
    return callCloudApi('adminLogin', { username, password });
  },

  // 验证token
  verifyToken: async (): Promise<CloudApiResponse<{ admin: any }>> => {
    return callCloudApi('verifyToken');
  },

  // 退出登录
  logout: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('adminLogout');
  },

  // 刷新token
  refreshToken: async (): Promise<CloudApiResponse<{ token: string }>> => {
    return callCloudApi('refreshToken');
  },

  // 修改密码
  changePassword: async (oldPassword: string, newPassword: string): Promise<CloudApiResponse<any>> => {
    return callCloudApi('changePassword', { oldPassword, newPassword });
  },
};

// 系统管理API
export const systemApi = {
  // 获取系统配置
  getSystemConfig: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('getSystemConfig');
  },

  // 更新系统配置
  updateSystemConfig: async (config: any): Promise<CloudApiResponse<any>> => {
    return callCloudApi('updateSystemConfig', config);
  },

  // 获取操作日志
  getOperationLogs: async (params?: {
    page?: number;
    limit?: number;
    userId?: string;
    action?: string;
    dateRange?: [string, string];
  }): Promise<CloudApiResponse<any[]>> => {
    return callCloudApi('getOperationLogs', params);
  },

  // 清理系统数据
  cleanupSystemData: async (type: string, days: number): Promise<CloudApiResponse<any>> => {
    return callCloudApi('cleanupSystemData', { type, days });
  },

  // 系统健康检查
  healthCheck: async (): Promise<CloudApiResponse<any>> => {
    return callCloudApi('healthCheck');
  },
};

// 导出默认实例
export default cloudApi;

// 导出类型
export type { CloudApiResponse };