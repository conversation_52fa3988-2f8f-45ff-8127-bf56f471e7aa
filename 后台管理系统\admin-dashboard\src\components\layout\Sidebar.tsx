import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { PermissionCheck } from '@/components/auth/ProtectedRoute';
import {
  LayoutDashboard,
  Users,
  ShoppingCart,
  MessageSquare,
  Wallet,
  Bell,
  Star,
  ChevronLeft,
  ChevronRight,
  Settings,
  FileText,
  Activity
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const menuItems = [
  {
    title: '仪表盘',
    href: '/dashboard',
    icon: LayoutDashboard,
    permission: 'dashboard.view' as const,
  },
  {
    title: '用户管理',
    href: '/users',
    icon: Users,
    permission: 'users.view' as const,
  },
  {
    title: '订单管理',
    href: '/orders',
    icon: ShoppingCart,
    permission: 'orders.view' as const,
  },
  {
    title: '聊天监控',
    href: '/chat',
    icon: MessageSquare,
    permission: 'chat.view' as const,
  },
  {
    title: '钱包管理',
    href: '/wallet',
    icon: Wallet,
    permission: 'wallet.view' as const,
  },
  {
    title: '通知管理',
    href: '/notifications',
    icon: Bell,
    permission: 'notifications.view' as const,
  },
  {
    title: '评价管理',
    href: '/evaluations',
    icon: Star,
    permission: 'evaluations.view' as const,
  },
  {
    title: '操作日志',
    href: '/system/logs',
    icon: FileText,
    permission: 'system.logs' as const,
  },
  {
    title: 'API测试',
    href: '/debug/api',
    icon: Activity,
    permission: 'system.debug' as const,
  },
];

export default function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const location = useLocation();

  return (
    <div className={cn(
      "fixed left-0 top-0 z-40 h-screen bg-white border-r border-gray-200 transition-all duration-300",
      isOpen ? "w-64" : "w-16"
    )}>
      {/* Logo区域 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {isOpen && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">三</span>
            </div>
            <span className="font-semibold text-gray-900">三角洲管理后台</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggle}
          className="p-1.5"
        >
          {isOpen ? (
            <ChevronLeft className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* 导航菜单 */}
      <nav className="p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.href;

          return (
            <PermissionCheck key={item.href} permission={item.permission}>
              <Link
                to={item.href}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  isActive
                    ? "bg-blue-100 text-blue-700"
                    : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                )}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                {isOpen && <span>{item.title}</span>}
              </Link>
            </PermissionCheck>
          );
        })}
      </nav>
    </div>
  );
}